# Calculator Application - Method Overloading in Java

A comprehensive calculator application demonstrating **method overloading** concepts in Java with a user-friendly menu-driven interface.

## 🎯 Project Objectives

- Understand and apply method overloading in Java
- Implement overloaded methods for arithmetic operations
- Use Java control structures and I/O operations
- Handle different data types and parameter combinations
- Demonstrate exception handling for robust applications

## 🚀 Features

- **Method Overloading**: Multiple `add()` methods with different parameter types and counts
- **Arithmetic Operations**: Addition, Subtraction, Multiplication, Division
- **Exception Handling**: Proper handling of divide-by-zero and invalid inputs
- **User-Friendly Interface**: Interactive menu-driven console application
- **Input Validation**: Comprehensive error handling for user inputs

## 📁 Project Structure

```
assignment-2/
├── Calculator.java      # Core calculator class with overloaded methods
├── UserInterface.java   # Menu-driven user interface
├── CalculatorApp.java   # Main application entry point
├── CalculatorTest.java  # Test class for functionality verification
└── README.md           # Project documentation
```

## 🔧 Class Overview

### Calculator Class
- `add(int a, int b)` - Adds two integers
- `add(double a, double b)` - Adds two double values
- `add(int a, int b, int c)` - Adds three integers
- `subtract(int a, int b)` - Subtracts two integers
- `multiply(double a, double b)` - Multiplies two double values
- `divide(int a, int b)` - Divides two integers with exception handling

### UserInterface Class
- `mainMenu()` - Displays menu and handles user choices
- `performAddition()` - Handles addition operations with type selection
- `performSubtraction()` - Handles subtraction operations
- `performMultiplication()` - Handles multiplication operations
- `performDivision()` - Handles division with error handling

## 🎮 How to Run

1. **Compile the application:**
   ```bash
   javac *.java
   ```

2. **Run the main application:**
   ```bash
   java CalculatorApp
   ```

3. **Run tests:**
   ```bash
   java CalculatorTest
   ```

## 📊 Application Flow Diagram

```mermaid
flowchart TD
    A[Start Application] --> B[Create UserInterface Object]
    B --> C[Display Welcome Message]
    C --> D[Show Main Menu]
    
    D --> E{User Choice}
    
    E -->|1| F[Addition Menu]
    E -->|2| G[Subtraction]
    E -->|3| H[Multiplication]
    E -->|4| I[Division]
    E -->|5| J[Exit Application]
    E -->|Invalid| K[Show Error Message]
    
    F --> F1{Addition Type}
    F1 -->|1| F2[Two Integers<br/>add(int, int)]
    F1 -->|2| F3[Two Doubles<br/>add(double, double)]
    F1 -->|3| F4[Three Integers<br/>add(int, int, int)]
    F1 -->|Invalid| F5[Invalid Choice Error]
    
    G --> G1[Input Two Integers]
    G1 --> G2[Call subtract(int, int)]
    G2 --> G3[Display Result]
    
    H --> H1[Input Two Doubles]
    H1 --> H2[Call multiply(double, double)]
    H2 --> H3[Display Result]
    
    I --> I1[Input Two Integers]
    I1 --> I2[Call divide(int, int)]
    I2 --> I3{Division by Zero?}
    I3 -->|Yes| I4[Throw ArithmeticException]
    I3 -->|No| I5[Calculate & Display Result]
    
    F2 --> L[Return to Main Menu]
    F3 --> L
    F4 --> L
    F5 --> L
    G3 --> L
    H3 --> L
    I4 --> L
    I5 --> L
    K --> L
    
    L --> D
    J --> M[Close Scanner & Exit]
    
    style A fill:#e1f5fe
    style J fill:#ffebee
    style F2 fill:#f3e5f5
    style F3 fill:#f3e5f5
    style F4 fill:#f3e5f5
    style I4 fill:#ffcdd2
```

## 🏗️ Class Relationship Diagram

```mermaid
classDiagram
    class Calculator {
        +add(int a, int b) int
        +add(double a, double b) double
        +add(int a, int b, int c) int
        +subtract(int a, int b) int
        +multiply(double a, double b) double
        +divide(int a, int b) double
    }

    class UserInterface {
        -Scanner sc
        -Calculator calc
        +UserInterface()
        +mainMenu() void
        +performAddition() void
        +performSubtraction() void
        +performMultiplication() void
        +performDivision() void
    }

    class CalculatorApp {
        +main(String[] args) void
    }

    class CalculatorTest {
        +main(String[] args) void
    }

    UserInterface --> Calculator : uses
    CalculatorApp --> UserInterface : creates
    CalculatorTest --> Calculator : tests

    note for Calculator "Method Overloading:\n- add() has 3 variants\n- Different parameter types\n- Different parameter counts"
```

## 🔄 Method Overloading Demonstration

```mermaid
graph LR
    A[add Method] --> B[add(int, int)]
    A --> C[add(double, double)]
    A --> D[add(int, int, int)]

    B --> B1[Parameters: 2 integers<br/>Return: int<br/>Example: add(5, 3) = 8]
    C --> C1[Parameters: 2 doubles<br/>Return: double<br/>Example: add(5.5, 3.2) = 8.7]
    D --> D1[Parameters: 3 integers<br/>Return: int<br/>Example: add(5, 3, 2) = 10]

    style A fill:#ffeb3b
    style B fill:#4caf50
    style C fill:#2196f3
    style D fill:#ff9800
```

## 🛡️ Exception Handling Flow

```mermaid
sequenceDiagram
    participant U as User
    participant UI as UserInterface
    participant C as Calculator

    U->>UI: Select Division (4)
    UI->>U: Request dividend
    U->>UI: Enter dividend (10)
    UI->>U: Request divisor
    U->>UI: Enter divisor (0)
    UI->>C: divide(10, 0)

    alt Divisor is 0
        C->>C: Check if b == 0
        C-->>UI: throw ArithmeticException("Division by zero is invalid sir")
        UI->>U: Display error message
    else Divisor is not 0
        C->>C: Calculate (double) a / b
        C-->>UI: Return result
        UI->>U: Display result
    end
```

## 📝 Sample Interaction

```
Welcome to the Calculator Application!

=== CALCULATOR MENU ===
1. Add Numbers
2. Subtract Numbers
3. Multiply Numbers
4. Divide Numbers
5. Exit
Enter your choice: 1

=== ADDITION ===
Choose addition type:
1. Add two integers
2. Add two decimal numbers
3. Add three integers
Enter your choice: 1
Enter first integer: 10
Enter second integer: 20
Result: 10 + 20 = 30
```

## ✅ Learning Outcomes

- ✅ **Method Overloading**: Implemented multiple methods with same name but different signatures
- ✅ **Polymorphism**: Demonstrated compile-time polymorphism through method overloading
- ✅ **Exception Handling**: Proper try-catch blocks for robust error management
- ✅ **Object-Oriented Design**: Clean separation of concerns across classes
- ✅ **User Interface Design**: Intuitive menu-driven interaction flow

## 🎯 Key Features Demonstrated

| Feature | Implementation | Example |
|---------|---------------|---------|
| **Method Overloading** | 3 different `add()` methods | `add(int,int)`, `add(double,double)`, `add(int,int,int)` |
| **Exception Handling** | Try-catch blocks | Division by zero handling |
| **Input Validation** | InputMismatchException handling | Invalid number format detection |
| **Menu Navigation** | Switch-case statements | User choice processing |
| **Data Type Handling** | int, double parameters | Type-specific operations |

## 🚀 Getting Started

1. Clone this repository
2. Compile: `javac *.java`
3. Run: `java CalculatorApp`
4. Follow the interactive menu prompts

---

**Assignment-2 | Method Overloading in Java | Calculator Application**
