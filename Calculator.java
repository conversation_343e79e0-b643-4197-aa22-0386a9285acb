/**
 * Calculator class demonstrating method overloading in Java
 * Provides arithmetic operations with different parameter types and counts
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class Calculator {
    
    /**
     * Adds two integers
     * @param a first integer
     * @param b second integer
     * @return sum of a and b
     */
    public int add(int a, int b) {
        return a + b;
    }
    
    /**
     * Adds two double values
     * @param a first double value
     * @param b second double value
     * @return sum of a and b
     */
    public double add(double a, double b) {
        return a + b;
    }
    
    /**
     * Adds three integers
     * @param a first integer
     * @param b second integer
     * @param c third integer
     * @return sum of a, b, and c
     */
    public int add(int a, int b, int c) {
        return a + b + c;
    }
    
    /**
     * Subtracts second integer from first integer
     * @param a first integer (minuend)
     * @param b second integer (subtrahend)
     * @return difference of a and b
     */
    public int subtract(int a, int b) {
        return a - b;
    }
    
    /**
     * Multiplies two double values
     * @param a first double value
     * @param b second double value
     * @return product of a and b
     */
    public double multiply(double a, double b) {
        return a * b;
    }
    
    /**
     * Divides first integer by second integer
     * Handles divide-by-zero exception
     * @param a dividend (integer)
     * @param b divisor (integer)
     * @return quotient of a divided by b
     * @throws ArithmeticException if divisor is zero
     */
    public double divide(int a, int b) throws ArithmeticException {
        if (b == 0) {
            throw new ArithmeticException("Division by zero is not allowed!");
        }
        return (double) a / b;
    }
}
