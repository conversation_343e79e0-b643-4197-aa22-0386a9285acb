import java.util.Scanner;
import java.util.InputMismatchException;

/**
 * UserInterface class for the Calculator Application
 * Provides menu-driven interface and handles user interactions
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class UserInterface {
    
    // Scanner object for user input
    private Scanner scanner;
    private Calculator calculator;
    
    /**
     * Constructor initializes Scanner and Calculator objects
     */
    public UserInterface() {
        scanner = new Scanner(System.in);
        calculator = new Calculator();
    }
    
    /**
     * Displays the main menu and handles user choices
     */
    public void mainMenu() {
        boolean continueProgram = true;
        
        System.out.println("Welcome to the Calculator Application!");
        
        while (continueProgram) {
            System.out.println("\n=== CALCULATOR MENU ===");
            System.out.println("1. Add Numbers");
            System.out.println("2. Subtract Numbers");
            System.out.println("3. Multiply Numbers");
            System.out.println("4. Divide Numbers");
            System.out.println("5. Exit");
            System.out.print("Enter your choice: ");
            
            try {
                int choice = scanner.nextInt();
                
                switch (choice) {
                    case 1:
                        performAddition();
                        break;
                    case 2:
                        performSubtraction();
                        break;
                    case 3:
                        performMultiplication();
                        break;
                    case 4:
                        performDivision();
                        break;
                    case 5:
                        System.out.println("Thank you for using the Calculator Application!");
                        continueProgram = false;
                        break;
                    default:
                        System.out.println("Invalid choice! Please enter a number between 1-5.");
                }
            } catch (InputMismatchException e) {
                System.out.println("Invalid input! Please enter a valid number.");
                scanner.nextLine(); // Clear the invalid input
            }
        }
        
        scanner.close();
    }
    
    /**
     * Handles addition operations with different overloaded methods
     */
    public void performAddition() {
        System.out.println("\n=== ADDITION ===");
        System.out.println("Choose addition type:");
        System.out.println("1. Add two integers");
        System.out.println("2. Add two decimal numbers");
        System.out.println("3. Add three integers");
        System.out.print("Enter your choice: ");
        
        try {
            int addChoice = scanner.nextInt();
            
            switch (addChoice) {
                case 1:
                    System.out.print("Enter first integer: ");
                    int int1 = scanner.nextInt();
                    System.out.print("Enter second integer: ");
                    int int2 = scanner.nextInt();
                    int result1 = calculator.add(int1, int2);
                    System.out.println("Result: " + int1 + " + " + int2 + " = " + result1);
                    break;
                    
                case 2:
                    System.out.print("Enter first decimal number: ");
                    double double1 = scanner.nextDouble();
                    System.out.print("Enter second decimal number: ");
                    double double2 = scanner.nextDouble();
                    double result2 = calculator.add(double1, double2);
                    System.out.println("Result: " + double1 + " + " + double2 + " = " + result2);
                    break;
                    
                case 3:
                    System.out.print("Enter first integer: ");
                    int int3 = scanner.nextInt();
                    System.out.print("Enter second integer: ");
                    int int4 = scanner.nextInt();
                    System.out.print("Enter third integer: ");
                    int int5 = scanner.nextInt();
                    int result3 = calculator.add(int3, int4, int5);
                    System.out.println("Result: " + int3 + " + " + int4 + " + " + int5 + " = " + result3);
                    break;
                    
                default:
                    System.out.println("Invalid choice for addition type!");
            }
        } catch (InputMismatchException e) {
            System.out.println("Invalid input! Please enter valid numbers.");
            scanner.nextLine(); // Clear the invalid input
        }
    }
    
    /**
     * Handles subtraction operations
     */
    public void performSubtraction() {
        System.out.println("\n=== SUBTRACTION ===");
        try {
            System.out.print("Enter first integer: ");
            int a = scanner.nextInt();
            System.out.print("Enter second integer: ");
            int b = scanner.nextInt();
            
            int result = calculator.subtract(a, b);
            System.out.println("Result: " + a + " - " + b + " = " + result);
        } catch (InputMismatchException e) {
            System.out.println("Invalid input! Please enter valid integers.");
            scanner.nextLine(); // Clear the invalid input
        }
    }
    
    /**
     * Handles multiplication operations
     */
    public void performMultiplication() {
        System.out.println("\n=== MULTIPLICATION ===");
        try {
            System.out.print("Enter first decimal number: ");
            double a = scanner.nextDouble();
            System.out.print("Enter second decimal number: ");
            double b = scanner.nextDouble();
            
            double result = calculator.multiply(a, b);
            System.out.println("Result: " + a + " × " + b + " = " + result);
        } catch (InputMismatchException e) {
            System.out.println("Invalid input! Please enter valid decimal numbers.");
            scanner.nextLine(); // Clear the invalid input
        }
    }
    
    /**
     * Handles division operations with exception handling
     */
    public void performDivision() {
        System.out.println("\n=== DIVISION ===");
        try {
            System.out.print("Enter dividend (integer): ");
            int a = scanner.nextInt();
            System.out.print("Enter divisor (integer): ");
            int b = scanner.nextInt();
            
            double result = calculator.divide(a, b);
            System.out.println("Result: " + a + " ÷ " + b + " = " + result);
        } catch (ArithmeticException e) {
            System.out.println("Error: " + e.getMessage());
        } catch (InputMismatchException e) {
            System.out.println("Invalid input! Please enter valid integers.");
            scanner.nextLine(); // Clear the invalid input
        }
    }
}
