/**
 * Test class to demonstrate Calculator functionality and method overloading
 * This class tests all overloaded methods and exception handling
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class CalculatorTest {
    
    public static void main(String[] args) {
        Calculator calc = new Calculator();
        
        System.out.println("=== CALCULATOR METHOD OVERLOADING DEMONSTRATION ===\n");
        
        // Test add method overloading
        System.out.println("1. ADDITION METHOD OVERLOADING:");
        System.out.println("   add(int, int): " + calc.add(10, 20) + " (Result: 30)");
        System.out.println("   add(double, double): " + calc.add(10.5, 20.3) + " (Result: 30.8)");
        System.out.println("   add(int, int, int): " + calc.add(10, 20, 30) + " (Result: 60)");
        
        // Test subtract method
        System.out.println("\n2. SUBTRACTION:");
        System.out.println("   subtract(int, int): " + calc.subtract(50, 20) + " (Result: 30)");
        
        // Test multiply method
        System.out.println("\n3. MULTIPLICATION:");
        System.out.println("   multiply(double, double): " + calc.multiply(4.5, 2.0) + " (Result: 9.0)");
        
        // Test divide method
        System.out.println("\n4. DIVISION:");
        try {
            System.out.println("   divide(int, int): " + calc.divide(20, 4) + " (Result: 5.0)");
        } catch (ArithmeticException e) {
            System.out.println("   Error: " + e.getMessage());
        }
        
        // Test divide by zero exception
        System.out.println("\n5. EXCEPTION HANDLING (Divide by Zero):");
        try {
            System.out.println("   divide(10, 0): " + calc.divide(10, 0));
        } catch (ArithmeticException e) {
            System.out.println("   Caught Exception: " + e.getMessage());
        }
        
        System.out.println("\n=== METHOD OVERLOADING VERIFICATION ===");
        System.out.println("✓ add(int, int) - Overloaded method 1");
        System.out.println("✓ add(double, double) - Overloaded method 2");
        System.out.println("✓ add(int, int, int) - Overloaded method 3");
        System.out.println("✓ subtract(int, int) - Single method");
        System.out.println("✓ multiply(double, double) - Single method");
        System.out.println("✓ divide(int, int) - Single method with exception handling");
        
        System.out.println("\n=== ALL TESTS COMPLETED SUCCESSFULLY ===");
    }
}
