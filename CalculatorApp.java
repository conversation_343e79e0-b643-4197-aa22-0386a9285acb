/**
 * Main application class for the Calculator Application
 * Demonstrates method overloading and object-oriented programming concepts
 * 
 * This application showcases:
 * - Method overloading with different parameter types and counts
 * - Exception handling for divide-by-zero scenarios
 * - User-friendly menu-driven interface
 * - Input validation and error handling
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class CalculatorApp {
    
    /**
     * Main method - entry point of the application
     * Creates UserInterface object and starts the calculator application
     * 
     * @param args command line arguments (not used)
     */
    public static void main(String[] args) {
        // Create an instance of UserInterface to start the application
        UserInterface ui = new UserInterface();
        
        // Start the main menu loop
        ui.mainMenu();
    }
}
